-- SQL to get the AssessmentId of the most recently updated assessment 
-- that has more than one "linked" drawings uploaded
--
-- "Linked" drawings are defined as:
-- 1. Multiple separate files (different DocumentId values) - these are truly separate files
-- 2. OR multi-page PDFs that have been split (same DocumentId, different PageNumber)
--
-- This query prioritizes assessments with multiple separate files first,
-- then falls back to multi-page PDFs if no separate files are found.

WITH AssessmentDrawingCounts AS (
    -- Get assessments with multiple separate drawing files (different DocumentIds)
    SELECT 
        a.AssessmentId,
        a.ModifiedOn AS AssessmentModifiedOn,
        COUNT(DISTINCT ad.DocumentId) AS UniqueDocumentCount,
        COUNT(ad.AssessmentDrawingId) AS TotalDrawingCount,
        'Multiple Separate Files' AS DrawingType
    FROM RSS_Assessment a
    INNER JOIN RSS_AssessmentDrawing ad ON a.AssessmentId = ad.AssessmentId
    INNER JOIN RSS_File f ON ad.Attachment = f.FileId
    WHERE 
        ad.Deleted = 0 
        AND ad.Archived = 0
        AND ad.ProcessingStatus = 'COMPLETE'
        AND ad.DocumentId IS NOT NULL
        AND f.Deleted = 0
    GROUP BY a.AssessmentId, a.ModifiedOn
    HAVING COUNT(DISTINCT ad.DocumentId) > 1  -- Multiple separate files
    
    UNION ALL
    
    -- Get assessments with multi-page PDFs (same DocumentId, multiple pages)
    SELECT 
        a.AssessmentId,
        a.ModifiedOn AS AssessmentModifiedOn,
        COUNT(DISTINCT ad.DocumentId) AS UniqueDocumentCount,
        COUNT(ad.AssessmentDrawingId) AS TotalDrawingCount,
        'Multi-page PDF' AS DrawingType
    FROM RSS_Assessment a
    INNER JOIN RSS_AssessmentDrawing ad ON a.AssessmentId = ad.AssessmentId
    INNER JOIN RSS_File f ON ad.Attachment = f.FileId
    WHERE 
        ad.Deleted = 0 
        AND ad.Archived = 0
        AND ad.ProcessingStatus = 'COMPLETE'
        AND ad.DocumentId IS NOT NULL
        AND f.Deleted = 0
        AND f.OriginalFileUrl IS NOT NULL  -- Indicates this is a split page from multi-page PDF
    GROUP BY a.AssessmentId, a.ModifiedOn
    HAVING COUNT(ad.AssessmentDrawingId) > 1  -- Multiple pages from same document
),
RankedAssessments AS (
    SELECT 
        AssessmentId,
        AssessmentModifiedOn,
        UniqueDocumentCount,
        TotalDrawingCount,
        DrawingType,
        -- Prioritize multiple separate files over multi-page PDFs
        ROW_NUMBER() OVER (
            ORDER BY 
                CASE WHEN DrawingType = 'Multiple Separate Files' THEN 1 ELSE 2 END,
                AssessmentModifiedOn DESC
        ) AS RowNum
    FROM AssessmentDrawingCounts
)
SELECT TOP 1
    ra.AssessmentId,
    ra.AssessmentModifiedOn,
    ra.UniqueDocumentCount,
    ra.TotalDrawingCount,
    ra.DrawingType,
    -- Additional assessment details
    a.StatusCode,
    apd.ClientJobNumber,
    apd.HouseNumber + ' ' + apd.StreetName + ', ' + apd.Suburb + ' ' + apd.StateCode AS FullAddress
FROM RankedAssessments ra
INNER JOIN RSS_Assessment a ON ra.AssessmentId = a.AssessmentId
LEFT JOIN RSS_AssessmentProjectDetail apd ON a.AssessmentId = apd.AssessmentId
WHERE ra.RowNum = 1;

-- Alternative simpler query if you just want the AssessmentId:
/*
SELECT TOP 1 a.AssessmentId
FROM RSS_Assessment a
INNER JOIN RSS_AssessmentDrawing ad ON a.AssessmentId = ad.AssessmentId
INNER JOIN RSS_File f ON ad.Attachment = f.FileId
WHERE 
    ad.Deleted = 0 
    AND ad.Archived = 0
    AND ad.ProcessingStatus = 'COMPLETE'
    AND ad.DocumentId IS NOT NULL
    AND f.Deleted = 0
GROUP BY a.AssessmentId, a.ModifiedOn
HAVING COUNT(DISTINCT ad.DocumentId) > 1  -- Multiple separate files
ORDER BY a.ModifiedOn DESC;
*/
