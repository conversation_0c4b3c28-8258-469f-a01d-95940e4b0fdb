-- Add DisplayDescription field to RSS_ServiceTemplateView
-- This script updates the RSS_ServiceTemplateView to include the DisplayDescription field
-- which already exists in the RSS_ServiceTemplate table but was missing from the view

-- Drop the existing view
DROP VIEW dbo.RSS_ServiceTemplateView
GO

-- Recreate the view with DisplayDescription included
CREATE VIEW dbo.RSS_ServiceTemplateView
AS
SELECT  template.ServiceTemplateId,
        template.ServiceCategoryCode,
        category.Title as ServiceCategoryTitle,
        template.Description,
        template.DisplayDescription,
        manufacturer.[Description] AS ManufacturerDescription,
        template.ManufacturerId,
        template.Comments,
        template.Deleted,
        template.CreatedOn,
        template.IsFavourite
FROM dbo.RSS_ServiceTemplate template
         JOIN dbo.RSS_ServiceCategory category ON category.ServiceCategoryCode = template.ServiceCategoryCode
         JOIN dbo.RSS_Manufacturer manufacturer ON manufacturer.ManufacturerId = template.ManufacturerId
GO